http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_left.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_right.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/scale_in.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_primary.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_statistics.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/priority_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_tasks.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_account.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_google.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/task_item_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fab_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_accent.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_card_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/google_sign_in_button.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_app_logo.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/main_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_sort.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_statistics.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/empty_state.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processDebugGoogleServices/values/values.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,+anim:slide_out_left,0,F;slide_in_right,1,F;fade_in,2,F;scale_in,3,F;+array:category_options,4,V400020037,**********,;@string/category_work,@string/category_personal,@string/category_shopping,@string/category_health,@string/category_education,@string/category_other,;+color:error_50,5,V4002405de,2a00240604,;"#fef2f2";text_primary,5,V4003508fc,2e00350926,;"#1C1C1C";neutral_600,5,V4000a019d,2d000a01c6,;"#525252";warning_500,5,V400220582,2d002205ab,;"#f59e0b";neutral_400,5,V400080141,2d0008016a,;"#a3a3a3";neutral_200,5,V4000600e5,2d0006010e,;"#e5e5e5";success_600,5,V400200527,2d00200550,;"#059669";success_50,5,V4001e04cc,2c001e04f4,;"#ecfdf5";surface_color,5,V400380988,2f003809b3,;"#FAFAFA";white,5,V4002a06ce,29002a06f3,;"#FFFFFFFF";error_500,5,V400250609,2b00250630,;"#ef4444";primary_950,5,V4001b047a,2d001b04a3,;"#172554";background_pending,5,V400320857,**********,;"#FFF8E1";success_color,5,V4003c0a48,2f003c0a73,;"#00C853";card_background,5,V4003909b8,31003909e5,;"#FFFFFF";neutral_950,5,V4000e0255,2d000e027e,;"#0a0a0a";primary_color,5,V4002b06f8,2f002b0723,;"#6200EE";warning_50,5,V400210555,2c0021057d,;"#fffbeb";primary_800,5,V40019041e,2d00190447,;"#1e40af";neutral_900,5,V4000d0227,2d000d0250,;"#171717";primary_600,5,V4001703c2,2d001703eb,;"#2563eb";background_in_progress,5,V40033088c,38003308c0,;"#E3F2FD";error_color,5,V4003b0a1a,2d003b0a43,;"#B00020";primary_400,5,V400150366,2d0015038f,;"#60a5fa";primary_200,5,V40013030a,2d00130333,;"#bfdbfe";neutral_700,5,V4000b01cb,2d000b01f4,;"#404040";neutral_500,5,V40009016f,2d00090198,;"#737373";warning_600,5,V4002305b0,2d002305d9,;"#d97706";neutral_300,5,V400070113,2d0007013c,;"#d4d4d4";divider_color,5,V4003a09ea,2f003a0a15,;"#E0E0E0";neutral_100,5,V4000500b7,2d000500e0,;"#f5f5f5";neutral_50,5,V40004008a,2c000400b2,;"#fafafa";accent_color,5,V4002d075d,2e002d0787,;"#03DAC6";success_500,5,V4001f04f9,2d001f0522,;"#10b981";status_pending,5,V4002f07be,30002f07ea,;"#FF9800";background_completed,5,V4003408c5,36003408f7,;"#E8F5E8";warning_color,5,V4003d0a78,2f003d0aa3,;"#FF8F00";error_600,5,V400260635,2b0026065c,;"#dc2626";status_in_progress,5,V4003007ef,340030081f,;"#2196F3";status_completed,5,V400310824,3200310852,;"#4CAF50";secondary_color,5,V4002e078c,31002e07b9,;"#018786";neutral_0,5,V40003005e,2b00030085,;"#ffffff";primary_dark_color,5,V4002c0728,34002c0758,;"#3700B3";black,5,V4002906a4,29002906c9,;"#FF000000";primary_900,5,V4001a044c,2d001a0475,;"#1e3a8a";primary_700,5,V4001803f0,2d00180419,;"#1d4ed8";primary_500,5,V400160394,2d001603bd,;"#3b82f6";neutral_800,5,V4000c01f9,2d000c0222,;"#262626";primary_300,5,V400140338,2d00140361,;"#93c5fd";primary_50,5,V4001102af,2c001102d7,;"#eff6ff";primary_100,5,V4001202dc,2d00120305,;"#dbeafe";text_hint,5,V40037095c,2b00370983,;"#999999";text_secondary,5,V40036092b,**********,;"#666666";+drawable:gradient_primary,6,F;ic_filter,7,F;ic_statistics,8,F;ic_notification,9,F;ic_sort,10,F;edit_text_background,11,F;priority_background,12,F;ic_add,13,F;ic_empty_tasks,14,F;ic_account,15,F;ic_launcher_foreground,16,F;status_background,17,F;ic_launcher_background,18,F;ic_google,19,F;task_item_background,20,F;ic_calendar,21,F;fab_background,22,F;gradient_accent,23,F;modern_card_background,24,F;google_sign_in_button,25,F;ic_app_logo,26,F;+id:action_account,27,F;radioGroupSort,28,F;btnContinueOffline,29,F;spinnerFilterStatus,30,F;taskDescription,31,F;main,32,F;spinnerStatus,33,F;radioSortDeadline,28,F;radioSortTitle,28,F;highPriorityTasksTextView,34,F;editTextReminder,33,F;taskReminderTime,31,F;completionRateTextView,34,F;fabAddTask,32,F;spinnerPriority,33,F;taskStatus,31,F;buttonCancelSort,28,F;pendingTasksTextView,34,F;editTextDeadline,33,F;editTextDescription,33,F;main_statistics,34,F;imageViewCalendar,33,F;totalTasksTextView,34,F;radioSortStatus,28,F;buttonApplySort,28,F;priorityIndicator,31,F;action_restore,27,F;buttonAdd,33,F;radioSortPriority,28,F;radioSortDateCreated,28,F;action_statistics,27,F;completedTasksTextView,34,F;action_clear_all,27,F;buttonClearFilter,30,F;taskDate,31,F;buttonApplyFilter,30,F;editTextSearch,30,F;action_backup,27,F;btnGoogleSignIn,29,F;taskCompletedCheckbox,31,F;action_sort,27,F;taskDeadline,31,F;editTextTitle,33,F;taskPriority,31,F;swipeRefreshLayout,32,F;inProgressTasksTextView,34,F;radioSortCategory,28,F;imageViewReminderCalendar,33,F;toolbar,32,F;emptyStateView,32,F;buttonAddFirstTask,35,F;reminderLayout,31,F;buttonCancel,33,F;tasksCountTextView,32,F;spinnerCategory,33,F;lowPriorityTasksTextView,34,F;tasksListView,32,F;mediumPriorityTasksTextView,34,F;taskTitle,31,F;filterButton,32,F;+layout:activity_login,29,F;activity_statistics,34,F;activity_main,32,F;dialog_filter,30,F;dialog_add_task,33,F;dialog_sort,28,F;empty_state,35,F;task_item,31,F;+menu:main_menu,27,F;+mipmap:ic_launcher_round,36,F;ic_launcher_round,37,F;ic_launcher_round,38,F;ic_launcher_round,39,F;ic_launcher_round,40,F;ic_launcher,41,F;ic_launcher,42,F;ic_launcher,43,F;ic_launcher,44,F;ic_launcher,45,F;+string:task_description,46,V4000b0208,40000b0244,;"Tapşırıq Açıqlaması";cancel,46,V4000e02c0,2a000e02e6,;"Ləğv Et";sort_by_priority,46,V400500f9c,3c00500fd4,;"Prioritetə Görə";gcm_defaultSenderId,47,V400020037,4f00020082,;"**********";category_shopping,46,V400490e62,3800490e96,;"Alış-veriş";category_work,46,V400470e01,2c00470e29,;"İş";clear_all_confirmation,46,V400320a19,6400320a79,;"Bütün tapşırıqları silmək istədiyinizə əminsiniz?";sign_in_with_google,46,V4005e12e1,43005e1320,;"Google ilə Daxil Ol";error_empty_description,46,V4002e093e,60002e099a,;"Zəhmət olmasa tapşırıq açıqlaması daxil edin";add_task_button,46,V4000400ad,3d000400e6,;"Tapşırıq Əlavə Et";default_web_client_id,46,V400671512,5d0067156b,;"1\:**********\:android\:0667e607dae9df37d5f9fa";task_priority,46,V4000d0282,3d000d02bb,;"Tapşırıq Prioriteti";sync_failed,46,V4006614d4,3d0066150d,;"Sinxronizasiya xətası";project_id,47,V40007023c,4a00070282,;"todolist-c29fe";status_all,46,V400220740,2d00220769,;"Hamısı";overall_statistics,46,V4003a0ba4,3f003a0bdf,;"Ümumi Statistika";filter_tasks,46,V400100315,3e0010034f,;"Tapşırıqları Filterlə";all_tasks_cleared,46,V400360b05,4b00360b4c,;"Bütün tapşırıqlar təmizləndi!";sign_in_description,46,V4005d126f,71005d12dc,;"Google hesabınızla daxil olaraq məlumatlarınızı cloud-da saxlayın";no_tasks_title,46,V4001c05ff,3a001c0635,;"Tapşırıq yoxdur";category_personal,46,V400480e2e,3300480e5d,;"Şəxsi";no_tasks_subtitle,46,V4001d063a,68001d069e,;"İlk tapşırığınızı əlavə etmək üçün aşağıdakı düyməyə basın";sort_by_category,46,V400541085,3e005410bf,;"Kateqoriyaya Görə";search_tasks,46,V400120397,37001203ca,;"Tapşırıq Axtar";sign_in_title,46,V4005c1235,39005c126a,;"Hesaba Daxil Ol";select_deadline,46,V4005610f7,3a0056112d,;"Deadline seçin";apply_filter,46,V400150452,3900150487,;"Filtri Tətbiq Et";backup_export,46,V400420d3a,3300420d69,;"Export Et";completion_rate,46,V4003e0cad,3c003e0ce5,;"Tamamlanma Faizi";refresh_tasks,46,V4001f06e5,3d001f071e,;"Tapşırıqları Yenilə";delete_confirmation,46,V4003109bd,5b00310a14,;"Bu tapşırığı silmək istədiyinizə əminsiniz?";priority_statistics,46,V4003c0c26,46003c0c68,;"Prioritet Statistikası";sort_by_status,46,V400510fd9,370051100c,;"Statusa Görə";add_task,46,V4000500eb,2d00050114,;"Əlavə Et";error_empty_title,46,V4002d08e6,57002d0939,;"Zəhmət olmasa tapşırıq başlığı daxil edin";category_education,46,V4004b0ed1,35004b0f02,;"Təhsil";delete_task,46,V400180505,**********,;"Tapşırığı Sil";account_menu,46,V4006113c2,2e006113ec,;"Hesab";google_storage_bucket,47,V4000601d2,**********,;"todolist-c29fe.firebasestorage.app";clear_all_tasks,46,V400060119,460006015b,;"Bütün Tapşırıqları Təmizlə";no,46,V400340aa3,2300340ac2,;"Xeyr";google_app_id,47,V4000400ef,6a00040155,;"1\:**********\:android\:0667e607dae9df37d5f9fa";task_hint,46,V40003006e,3e000300a8,;"Yeni tapşırıq daxil edin";sort_tasks,46,V400410d0c,2d00410d35,;"Sırala";signed_in_as,46,V400641458,380064148c,;"Daxil olmuş\: %s";google_api_key,47,V400030087,67000300ea,;"AIzaSyBM9mpJi6JAxpbXl4NuAdRIbmi3nAV72tQ";total_tasks,46,V40016048c,3d001604c5,;"Ümumi Tapşırıqlar\: %d";filter_by_status,46,V400110354,**********,;"Statusa Görə Filterlə";select_reminder_time,46,V400581167,47005811aa,;"Xatırlatma vaxtı seçin";no_tasks,46,V4001b05bf,3f001b05fa,;"Heç bir tapşırıq tapılmadı";total_tasks_label,46,V4003d0c6d,3f003d0ca8,;"Ümumi Tapşırıqlar";task_updated,46,V40019053b,**********,;"Tapşırıq uğurla yeniləndi";status_pending,46,V40023076e,330023079d,;"Gözləyir";task_deadline,46,V4005510c4,32005510f2,;"Deadline";sort_by_date_created,46,V4004f0f53,48004f0f97,;"Yaradılma Tarixinə Görə";status_in_progress,46,V4002407a2,39002407d7,;"Davam Edir";status_completed,46,V4002507dc,370025080f,;"Tamamlandı";task_title,46,V4000a01d0,37000a0203,;"Tapşırıq Başlığı";edit_task,46,V4001704ca,3a00170500,;"Tapşırığı Redaktə Et";sign_out,46,V40063142d,2a00631453,;"Çıxış";task_status,46,V4000c0249,38000c027d,;"Tapşırıq Statusu";status_statistics,46,V4003b0be4,41003b0c21,;"Status Statistikası";login_subtitle,46,V4005b11d0,64005b1230,;"Tapşırıqlarınızı bütün cihazlarınızda sinxronizasiya edin";yes,46,V400330a7e,2400330a9e,;"Bəli";task_deleted,46,V4001a057e,40001a05ba,;"Tapşırıq uğurla silindi";search_hint,46,V4001303cf,4a00130415,;"Başlıq və ya açıqlamaya görə axtar";google_crash_reporting_api_key,47,V40005015a,77000501cd,;"AIzaSyBM9mpJi6JAxpbXl4NuAdRIbmi3nAV72tQ";priority_medium,46,V400290864,**********,;"Orta";category_other,46,V4004c0f07,30004c0f33,;"Digər";sort_by_title,46,V400521011,**********,;"Başlığa Görə";priority_low,46,V4002a0895,2e002a08bf,;"Aşağı";sync_success,46,V400651491,42006514cf,;"Sinxronizasiya tamamlandı";filter,46,V4000f02eb,29000f0310,;"Filter";app_name,46,V400020037,**********,;"Tapşırıq Siyahısı";add_new_task,46,V400090190,3f000901cb,;"Yeni Tapşırıq Əlavə Et";continue_without_account,46,V4005f1325,46005f1367,;"Hesabsız Davam Et";backup_import,46,V400430d6e,3300430d9d,;"Import Et";task_reminder,46,V400571132,**********,;"Xatırlatma";add_first_task,46,V4001e06a3,41001e06e0,;"İlk Tapşırığı Əlavə Et";login_footer,46,V40060136c,55006013bd,;"Məlumatlarınız təhlükəsiz şəkildə saxlanılır";sort_by_deadline,46,V400531048,3c00531080,;"Deadline-a Görə";priority_high,46,V400280833,300028085f,;"Yüksək";category_health,46,V4004a0e9b,35004a0ecc,;"Sağlamlıq";sync_now,46,V4006213f1,3b00621428,;"İndi Sinxronizasiya Et";task_category,46,V400440da2,3f00440ddd,;"Tapşırıq Kateqoriyası";clear_filter,46,V40014041a,370014044d,;"Filtri Təmizlə";task_added,46,V400350ac7,3d00350b00,;"Tapşırıq əlavə edildi!";statistics,46,V400390b72,3100390b9f,;"Statistika";+style:Base.Theme.Todolist,48,V400020072,c000c0305,;DTheme.AppCompat.Light.NoActionBar,colorPrimary:@color/primary_color,colorPrimaryDark:@color/primary_dark_color,colorAccent:@color/accent_color,android\:windowBackground:@color/surface_color,android\:textColorPrimary:@color/text_primary,android\:textColorSecondary:@color/text_secondary,android\:textColorHint:@color/text_hint,;Theme.Todolist,48,V4000e030b,40000e0347,;DBase.Theme.Todolist,;