<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.3" type="incidents">

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="255"
            endLine="13"
            endColumn="21"
            endOffset="267"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.7.3 is available: 8.11.0">
        <fix-replace
            description="Change to 8.11.0"
            family="Update versions"
            oldString="8.7.3"
            replacement="8.11.0"
            priority="0"/>
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0">
        <fix-replace
            description="Change to 33.16.0"
            family="Update versions"
            oldString="32.7.0"
            replacement="33.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1157"
            endLine="47"
            endColumn="71"
            endOffset="1208"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.gms:play-services-auth than 20.7.0 is available: 21.3.0">
        <fix-replace
            description="Change to 21.3.0"
            family="Update versions"
            oldString="20.7.0"
            replacement="21.3.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1343"
            endLine="50"
            endColumn="70"
            endOffset="1393"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="6"
            column="13"
            startOffset="100"
            endLine="6"
            endColumn="20"
            endOffset="107"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="8"
            column="12"
            startOffset="139"
            endLine="8"
            endColumn="19"
            endOffset="146"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@android:color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@android:color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="17"
            column="9"
            startOffset="606"
            endLine="17"
            endColumn="44"
            endOffset="641"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/neutral_0&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/neutral_0"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="55"
            column="25"
            startOffset="2450"
            endLine="55"
            endColumn="56"
            endOffset="2481"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/primary_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/primary_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="123"
            column="13"
            startOffset="4261"
            endLine="123"
            endColumn="48"
            endOffset="4296"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/primary_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/primary_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="167"
            column="13"
            startOffset="5877"
            endLine="167"
            endColumn="48"
            endOffset="5912"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_hint&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_hint"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/empty_state.xml"
            line="14"
            column="9"
            startOffset="477"
            endLine="14"
            endColumn="40"
            endOffset="508"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/NotificationHelper.java"
            line="96"
            column="28"
            startOffset="5098"
            endLine="96"
            endColumn="79"
            endOffset="5149"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="131"
            column="10"
            startOffset="5092"
            endLine="131"
            endColumn="22"
            endOffset="5104"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/neutral_100` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="8"
            column="5"
            startOffset="370"
            endLine="8"
            endColumn="44"
            endOffset="409"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/surface_color` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_statistics.xml"
            line="8"
            column="5"
            startOffset="381"
            endLine="8"
            endColumn="46"
            endOffset="422"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/surface_color` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/empty_state.xml"
            line="8"
            column="5"
            startOffset="288"
            endLine="8"
            endColumn="46"
            endOffset="329"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
                    startOffset="6202"
                    endOffset="6922"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
                    startOffset="6386"
                    endOffset="6693"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
                    startOffset="6703"
                    endOffset="6901"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="190"
            column="10"
            startOffset="6704"
            endLine="190"
            endColumn="16"
            endOffset="6710"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml"
                    startOffset="1802"
                    endOffset="2538"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml"
                    startOffset="1986"
                    endOffset="2304"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml"
                    startOffset="2314"
                    endOffset="2517"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml"
            line="67"
            column="10"
            startOffset="2315"
            endLine="67"
            endColumn="16"
            endOffset="2321"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_sort.xml"
                    startOffset="2676"
                    endOffset="3464"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_sort.xml"
                    startOffset="2907"
                    endOffset="3224"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_sort.xml"
                    startOffset="3236"
                    endOffset="3441"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_sort.xml"
            line="89"
            column="10"
            startOffset="3237"
            endLine="89"
            endColumn="16"
            endOffset="3243"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="61"
            column="17"
            startOffset="2377"
            endLine="61"
            endColumn="40"
            endOffset="2400"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="18"
            column="6"
            startOffset="603"
            endLine="18"
            endColumn="14"
            endOffset="611"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="30"
            column="6"
            startOffset="1021"
            endLine="30"
            endColumn="14"
            endOffset="1029"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="103"
            column="10"
            startOffset="3492"
            endLine="103"
            endColumn="18"
            endOffset="3500"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_task.xml"
            line="147"
            column="10"
            startOffset="5095"
            endLine="147"
            endColumn="18"
            endOffset="5103"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_filter.xml"
            line="41"
            column="6"
            startOffset="1383"
            endLine="41"
            endColumn="14"
            endOffset="1391"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for gson"
            robot="true">
            <fix-replace
                description="Replace with gson = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="227"
                    endOffset="227"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.gson"
                robot="true"
                replacement="libs.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="958"
                    endOffset="992"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="42"
            column="20"
            startOffset="958"
            endLine="42"
            endColumn="54"
            endOffset="992"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for swiperefreshlayout"
            robot="true">
            <fix-replace
                description="Replace with swiperefreshlayout = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="swiperefreshlayout = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="214"
                    endOffset="214"/>
            </fix-replace>
            <fix-replace
                description="Replace with swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayout&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayout&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="1046"
                    endOffset="1046"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.swiperefreshlayout"
                robot="true"
                replacement="libs.swiperefreshlayout"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1012"
                    endOffset="1066"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="43"
            column="20"
            startOffset="1012"
            endLine="43"
            endColumn="74"
            endOffset="1066"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for cardview"
            robot="true">
            <fix-replace
                description="Replace with cardview = &quot;1.0.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cardview = &quot;1.0.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with cardview = { module = &quot;androidx.cardview:cardview&quot;, version.ref = &quot;cardview&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="cardview = { module = &quot;androidx.cardview:cardview&quot;, version.ref = &quot;cardview&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="227"
                    endOffset="227"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.cardview"
                robot="true"
                replacement="libs.cardview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1086"
                    endOffset="1120"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="44"
            column="20"
            startOffset="1086"
            endLine="44"
            endColumn="54"
            endOffset="1120"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-bom"
            robot="true">
            <fix-replace
                description="Replace with firebaseBom = &quot;32.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseBom = &quot;32.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="227"
                    endOffset="227"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.bom"
                robot="true"
                replacement="libs.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1157"
                    endOffset="1208"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="47"
            column="20"
            startOffset="1157"
            endLine="47"
            endColumn="71"
            endOffset="1208"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-auth"
            robot="true">
            <fix-replace
                description="Replace with firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-auth = { module = &quot;com.google.firebase:firebase-auth&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="227"
                    endOffset="227"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.auth"
                robot="true"
                replacement="libs.firebase.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1228"
                    endOffset="1263"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="48"
            column="20"
            startOffset="1228"
            endLine="48"
            endColumn="55"
            endOffset="1263"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-firestore"
            robot="true">
            <fix-replace
                description="Replace with firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-firestore = { module = &quot;com.google.firebase:firebase-firestore&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="227"
                    endOffset="227"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.firestore"
                robot="true"
                replacement="libs.firebase.firestore"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1283"
                    endOffset="1323"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="49"
            column="20"
            startOffset="1283"
            endLine="49"
            endColumn="60"
            endOffset="1323"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for play-services-auth"
            robot="true">
            <fix-replace
                description="Replace with playServicesAuth = &quot;20.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="playServicesAuth = &quot;20.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="214"
                    endOffset="214"/>
            </fix-replace>
            <fix-replace
                description="Replace with play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuth&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="play-services-auth = { module = &quot;com.google.android.gms:play-services-auth&quot;, version.ref = &quot;playServicesAuth&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
                    startOffset="1046"
                    endOffset="1046"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.play.services.auth"
                robot="true"
                replacement="libs.play.services.auth"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1343"
                    endOffset="1393"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="50"
            column="20"
            startOffset="1343"
            endLine="50"
            endColumn="70"
            endOffset="1393"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="12"
            column="6"
            startOffset="426"
            endLine="12"
            endColumn="15"
            endOffset="435"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/empty_state.xml"
            line="10"
            column="6"
            startOffset="337"
            endLine="10"
            endColumn="15"
            endOffset="346"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/LoginActivity.java"
            line="46"
            column="37"
            startOffset="1357"
            endLine="46"
            endColumn="54"
            endOffset="1374"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="94"
            column="40"
            startOffset="3401"
            endLine="94"
            endColumn="85"
            endOffset="3446"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="95"
            column="44"
            startOffset="3492"
            endLine="95"
            endColumn="88"
            endOffset="3536"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="96"
            column="42"
            startOffset="3580"
            endLine="96"
            endColumn="84"
            endOffset="3622"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="97"
            column="45"
            startOffset="3669"
            endLine="97"
            endColumn="91"
            endOffset="3715"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="98"
            column="47"
            startOffset="3764"
            endLine="98"
            endColumn="88"
            endOffset="3805"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="99"
            column="49"
            startOffset="3856"
            endLine="99"
            endColumn="92"
            endOffset="3899"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="100"
            column="46"
            startOffset="3947"
            endLine="100"
            endColumn="86"
            endOffset="3987"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="101"
            column="44"
            startOffset="4033"
            endLine="101"
            endColumn="88"
            endOffset="4077"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="141"
            column="36"
            startOffset="5300"
            endLine="141"
            endColumn="93"
            endOffset="5357"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="142"
            column="40"
            startOffset="5399"
            endLine="142"
            endColumn="100"
            endOffset="5459"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="143"
            column="38"
            startOffset="5499"
            endLine="143"
            endColumn="94"
            endOffset="5555"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="144"
            column="41"
            startOffset="5598"
            endLine="144"
            endColumn="104"
            endOffset="5661"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="145"
            column="43"
            startOffset="5706"
            endLine="145"
            endColumn="103"
            endOffset="5766"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="146"
            column="45"
            startOffset="5813"
            endLine="146"
            endColumn="109"
            endOffset="5877"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="147"
            column="42"
            startOffset="5921"
            endLine="147"
            endColumn="100"
            endOffset="5979"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/StatisticsActivity.java"
            line="148"
            column="40"
            startOffset="6021"
            endLine="148"
            endColumn="149"
            endOffset="6130"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/TaskAdapter.java"
            line="92"
            column="45"
            startOffset="3731"
            endLine="92"
            endColumn="127"
            endOffset="3813"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Task Title&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="51"
            column="17"
            startOffset="1985"
            endLine="51"
            endColumn="42"
            endOffset="2010"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Orta&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="60"
            column="17"
            startOffset="2341"
            endLine="60"
            endColumn="36"
            endOffset="2360"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="77"
            column="17"
            startOffset="3105"
            endLine="77"
            endColumn="39"
            endOffset="3127"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Task description goes here...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="88"
            column="13"
            startOffset="3466"
            endLine="88"
            endColumn="57"
            endOffset="3510"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;01/01/2024 12:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="113"
            column="17"
            startOffset="4437"
            endLine="113"
            endColumn="48"
            endOffset="4468"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Deadline: Yoxdur&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="123"
            column="17"
            startOffset="4840"
            endLine="123"
            endColumn="48"
            endOffset="4871"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Reminder: 01/01/2024 11:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/task_item.xml"
            line="152"
            column="17"
            startOffset="5959"
            endLine="152"
            endColumn="58"
            endOffset="6000"/>
    </incident>

</incidents>
