package com.example.todolist;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.firebase.auth.FirebaseUser;

public class LoginActivity extends AppCompatActivity implements GoogleAuthHelper.AuthListener {

    private GoogleAuthHelper googleAuthHelper;
    private Button btnGoogleSignIn;
    private Button btnContinueOffline;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        initializeViews();
        setupGoogleAuth();
        setupClickListeners();
        
        // Check if user is already signed in
        if (googleAuthHelper.isSignedIn()) {
            navigateToMain();
        }
    }

    private void initializeViews() {
        btnGoogleSignIn = findViewById(R.id.btnGoogleSignIn);
        btnContinueOffline = findViewById(R.id.btnContinueOffline);
    }

    private void setupGoogleAuth() {
        googleAuthHelper = new GoogleAuthHelper(this, this);
    }

    private void setupClickListeners() {
        btnGoogleSignIn.setOnClickListener(v -> {
            btnGoogleSignIn.setEnabled(false);
            btnGoogleSignIn.setText("Giriş edilir...");
            googleAuthHelper.signIn();
        });

        btnContinueOffline.setOnClickListener(v -> {
            // Save offline mode preference
            SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
            prefs.edit().putBoolean("offline_mode", true).apply();
            navigateToMain();
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == GoogleAuthHelper.getSignInRequestCode()) {
            googleAuthHelper.handleSignInResult(data);
        }
    }

    @Override
    public void onSignInSuccess(FirebaseUser user) {
        // Save online mode preference
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        prefs.edit()
                .putBoolean("offline_mode", false)
                .putString("user_id", user.getUid())
                .putString("user_name", user.getDisplayName())
                .putString("user_email", user.getEmail())
                .apply();
        
        navigateToMain();
    }

    @Override
    public void onSignInFailure(String error) {
        btnGoogleSignIn.setEnabled(true);
        btnGoogleSignIn.setText(getString(R.string.sign_in_with_google));
        Toast.makeText(this, "Giriş xətası: " + error, Toast.LENGTH_LONG).show();
    }

    @Override
    public void onSignOutSuccess() {
        // This won't be called in LoginActivity
    }

    private void navigateToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        // Disable back button to prevent going back to splash/previous activity
        // User must choose an option
        super.onBackPressed();
    }
}
