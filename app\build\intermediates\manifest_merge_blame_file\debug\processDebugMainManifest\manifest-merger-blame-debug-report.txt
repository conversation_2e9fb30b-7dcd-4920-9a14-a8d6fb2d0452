1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.todolist"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:8:5-80
14-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:8:22-77
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:5-111
16-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:22-79
17    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
17-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:11:22-76
18
19    <permission
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:13:5-42:19
26        android:allowBackup="true"
26-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:extractNativeLibs="true"
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:15:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:16:9-41
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.Todolist" >
33-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:18:9-46
34        <activity
34-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:19:9-27:20
35            android:name="com.example.todolist.LoginActivity"
35-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:20:13-42
36            android:exported="true" >
36-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:21:13-36
37            <intent-filter>
37-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:22:13-26:29
38                <action android:name="android.intent.action.MAIN" />
38-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:23:17-69
38-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:23:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:25:17-77
40-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:25:27-74
41            </intent-filter>
42        </activity>
43        <activity
43-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:29:9-31:40
44            android:name="com.example.todolist.MainActivity"
44-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:30:13-41
45            android:exported="false" />
45-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:31:13-37
46        <activity
46-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:33:9-36:58
47            android:name="com.example.todolist.StatisticsActivity"
47-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:34:13-47
48            android:exported="false"
48-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:35:13-37
49            android:parentActivityName="com.example.todolist.MainActivity" />
49-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:36:13-55
50
51        <receiver
51-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:38:9-41:40
52            android:name="com.example.todolist.ReminderBroadcastReceiver"
52-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:39:13-54
53            android:enabled="true"
53-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:40:13-35
54            android:exported="false" />
54-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:41:13-37
55
56        <activity
56-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
57            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
57-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
58            android:excludeFromRecents="true"
58-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
59            android:exported="false"
59-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
60            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
60-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
61        <!--
62            Service handling Google Sign-In user revocation. For apps that do not integrate with
63            Google Sign-In, this service will never be started.
64        -->
65        <service
65-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
66            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
66-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
67            android:exported="true"
67-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
68            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
68-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
69            android:visibleToInstantApps="true" />
69-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
70
71        <activity
71-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
72            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
72-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
73            android:excludeFromRecents="true"
73-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
74            android:exported="true"
74-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
75            android:launchMode="singleTask"
75-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
76            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
76-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
77            <intent-filter>
77-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
78                <action android:name="android.intent.action.VIEW" />
78-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
78-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
79
80                <category android:name="android.intent.category.DEFAULT" />
80-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
80-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
81                <category android:name="android.intent.category.BROWSABLE" />
81-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
81-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
82
83                <data
83-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
84                    android:host="firebase.auth"
84-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
85                    android:path="/"
85-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
86                    android:scheme="genericidp" />
86-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
87            </intent-filter>
88        </activity>
89        <activity
89-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
90            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
90-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
91            android:excludeFromRecents="true"
91-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
92            android:exported="true"
92-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
93            android:launchMode="singleTask"
93-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
94            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
94-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
95            <intent-filter>
95-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
96                <action android:name="android.intent.action.VIEW" />
96-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
96-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
97
98                <category android:name="android.intent.category.DEFAULT" />
98-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
98-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
99                <category android:name="android.intent.category.BROWSABLE" />
99-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
99-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
100
101                <data
101-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
102                    android:host="firebase.auth"
102-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
103                    android:path="/"
103-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
104                    android:scheme="recaptcha" />
104-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
105            </intent-filter>
106        </activity>
107
108        <service
108-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
109            android:name="com.google.firebase.components.ComponentDiscoveryService"
109-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:67:13-84
110            android:directBootAware="true"
110-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
111            android:exported="false" >
111-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:68:13-37
112            <meta-data
112-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
113                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
113-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
115            <meta-data
115-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
116                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
116-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
118            <meta-data
118-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
119                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
119-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
121            <meta-data
121-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
122                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
122-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
124            <meta-data
124-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
125                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
125-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
127        </service>
128
129        <activity
129-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
130            android:name="com.google.android.gms.common.api.GoogleApiActivity"
130-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
131            android:exported="false"
131-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
132            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
132-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
133
134        <provider
134-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
135            android:name="com.google.firebase.provider.FirebaseInitProvider"
135-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
136            android:authorities="com.example.todolist.firebaseinitprovider"
136-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
138            android:exported="false"
138-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
139            android:initOrder="100" />
139-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
140        <provider
140-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
141            android:name="androidx.startup.InitializationProvider"
141-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
142            android:authorities="com.example.todolist.androidx-startup"
142-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
143            android:exported="false" >
143-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
144            <meta-data
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
145                android:name="androidx.emoji2.text.EmojiCompatInitializer"
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
146                android:value="androidx.startup" />
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
147            <meta-data
147-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
148-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
149                android:value="androidx.startup" />
149-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
150            <meta-data
150-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
151                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
151-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
152                android:value="androidx.startup" />
152-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
153        </provider>
154
155        <meta-data
155-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
156            android:name="com.google.android.gms.version"
156-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
157            android:value="@integer/google_play_services_version" />
157-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
158
159        <receiver
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
160            android:name="androidx.profileinstaller.ProfileInstallReceiver"
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
161            android:directBootAware="false"
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
162            android:enabled="true"
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
163            android:exported="true"
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
164            android:permission="android.permission.DUMP" >
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
166                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
167            </intent-filter>
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
169                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
170            </intent-filter>
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
172                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
175                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
176            </intent-filter>
177        </receiver>
178    </application>
179
180</manifest>
