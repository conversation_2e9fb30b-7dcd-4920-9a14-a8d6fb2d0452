<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.7.3" type="baseline" client="gradle" dependencies="false" name="AGP (8.7.3)" variant="all" version="8.7.3">

    <issue
        id="OldTargetA<PERSON>"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.3 is available: 8.11.0"
        errorLine1="agp = &quot;8.7.3&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.3 is available: 8.11.0"
        errorLine1="agp = &quot;8.7.3&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        message="A newer version of com.android.application than 8.7.3 is available: 8.11.0"
        errorLine1="agp = &quot;8.7.3&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0"
        errorLine1="    implementation platform(&apos;com.google.firebase:firebase-bom:32.7.0&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.gms:play-services-auth than 20.7.0 is available: 21.3.0"
        errorLine1="    implementation &apos;com.google.android.gms:play-services-auth:20.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1"
        errorLine1="appcompat = &quot;1.7.0&quot;"
        errorLine2="            ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1"
        errorLine1="activity = &quot;1.8.2&quot;"
        errorLine2="           ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1"
        errorLine1="activity = &quot;1.8.2&quot;"
        errorLine2="           ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1"
        errorLine1="activity = &quot;1.8.2&quot;"
        errorLine2="           ~~~~~~~">
        <location
            file="$HOME/Music/remote/todolist/gradle/libs.versions.toml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="        android:tint=&quot;@android:color/white&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="17"
            column="9"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="                        android:tint=&quot;@color/neutral_0&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="55"
            column="25"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/primary_color&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="123"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/primary_color&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="        android:tint=&quot;@color/text_hint&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/empty_state.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/NotificationHelper.java"
            line="96"
            column="28"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="131"
            column="10"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/neutral_100` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)"
        errorLine1="    android:background=&quot;@color/neutral_100&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/surface_color` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)"
        errorLine1="    android:background=&quot;@color/surface_color&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_statistics.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/surface_color` with a theme that also paints a background (inferred theme is `@style/Theme.Todolist`)"
        errorLine1="    android:background=&quot;@color/surface_color&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/empty_state.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_200&quot;>#e5e5e5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_300&quot;>#d4d4d4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_400&quot;>#a3a3a3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_800&quot;>#262626&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_950` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_950&quot;>#0a0a0a&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_100&quot;>#dbeafe&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_300&quot;>#93c5fd&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_400&quot;>#60a5fa&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_700&quot;>#1d4ed8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_800&quot;>#1e40af&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_900&quot;>#1e3a8a&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_950` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_950&quot;>#172554&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.success_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;success_500&quot;>#10b981&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning_50&quot;>#fffbeb&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.warning_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;warning_500&quot;>#f59e0b&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_50&quot;>#fef2f2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="37"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;error_500&quot;>#ef4444&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="43"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_pending` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_pending&quot;>#FFF8E1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_in_progress` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_in_progress&quot;>#E3F2FD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_completed` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_completed&quot;>#E8F5E8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.fab_background` appears to be unused"
        errorLine1="&lt;ripple xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/fab_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.gradient_accent` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/gradient_accent.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.mipmap.ic_launcher_round` appears to be unused">
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher_round.webp"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.modern_card_background` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/modern_card_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.task_hint` appears to be unused"
        errorLine1="    &lt;string name=&quot;task_hint&quot;>Yeni tapşırıq daxil edin&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_tasks` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_tasks&quot;>Heç bir tapşırıq tapılmadı&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sync_now` appears to be unused"
        errorLine1="    &lt;string name=&quot;sync_now&quot;>İndi Sinxronizasiya Et&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="99"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sign_out` appears to be unused"
        errorLine1="    &lt;string name=&quot;sign_out&quot;>Çıxış&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.signed_in_as` appears to be unused"
        errorLine1="    &lt;string name=&quot;signed_in_as&quot;>Daxil olmuş: %s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sync_success` appears to be unused"
        errorLine1="    &lt;string name=&quot;sync_success&quot;>Sinxronizasiya tamamlandı&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sync_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;sync_failed&quot;>Sinxronizasiya xətası&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="103"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.task_item_background` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/task_item_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="190"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="src/main/res/layout/dialog_filter.xml"
            line="67"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="src/main/res/layout/dialog_sort.xml"
            line="89"
            column="10"/>
    </issue>

    <issue
        id="SmallSp"
        message="Avoid using sizes smaller than `11sp`: `10sp`"
        errorLine1="                android:textSize=&quot;10sp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="61"
            column="17"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="18"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="30"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="103"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="147"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_filter.xml"
            line="41"
            column="6"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.code.gson:gson:2.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="42"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;androidx.swiperefreshlayout:swiperefreshlayout:1.1.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="43"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;androidx.cardview:cardview:1.0.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="44"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation platform(&apos;com.google.firebase:firebase-bom:32.7.0&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.firebase:firebase-auth&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="48"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.firebase:firebase-firestore&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="49"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation &apos;com.google.android.gms:play-services-auth:20.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="50"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="12"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/empty_state.xml"
            line="10"
            column="6"/>
    </issue>

    <issue
        id="KeyboardInaccessibleWidget"
        message="&apos;clickable&apos; attribute found, please also add &apos;focusable&apos;"
        errorLine1="            android:clickable=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="KeyboardInaccessibleWidget"
        message="&apos;clickable&apos; attribute found, please also add &apos;focusable&apos;"
        errorLine1="            android:clickable=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_add_task.xml"
            line="155"
            column="13"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            btnGoogleSignIn.setText(&quot;Giriş edilir...&quot;);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/LoginActivity.java"
            line="46"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            totalTasksTextView.setText(getString(R.string.total_tasks_label) + &quot;: 0&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="94"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            completedTasksTextView.setText(getString(R.string.status_completed) + &quot;: 0&quot;);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="95"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            pendingTasksTextView.setText(getString(R.string.status_pending) + &quot;: 0&quot;);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="96"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            inProgressTasksTextView.setText(getString(R.string.status_in_progress) + &quot;: 0&quot;);"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="97"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            highPriorityTasksTextView.setText(getString(R.string.priority_high) + &quot;: 0&quot;);"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="98"
            column="47"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + &quot;: 0&quot;);"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="99"
            column="49"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            lowPriorityTasksTextView.setText(getString(R.string.priority_low) + &quot;: 0&quot;);"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="100"
            column="46"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            completionRateTextView.setText(getString(R.string.completion_rate) + &quot;: 0%&quot;);"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="101"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        totalTasksTextView.setText(getString(R.string.total_tasks_label) + &quot;: &quot; + totalTasks);"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="141"
            column="36"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        completedTasksTextView.setText(getString(R.string.status_completed) + &quot;: &quot; + completedTasks);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="142"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        pendingTasksTextView.setText(getString(R.string.status_pending) + &quot;: &quot; + pendingTasks);"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="143"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        inProgressTasksTextView.setText(getString(R.string.status_in_progress) + &quot;: &quot; + inProgressTasks);"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="144"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        highPriorityTasksTextView.setText(getString(R.string.priority_high) + &quot;: &quot; + highPriorityTasks);"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="145"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + &quot;: &quot; + mediumPriorityTasks);"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="146"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        lowPriorityTasksTextView.setText(getString(R.string.priority_low) + &quot;: &quot; + lowPriorityTasks);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="147"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        completionRateTextView.setText(getString(R.string.completion_rate) + &quot;: &quot; + String.format(Locale.getDefault(), &quot;%.2f&quot;, completionRate) + &quot;%&quot;);"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/StatisticsActivity.java"
            line="148"
            column="40"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            holder.taskReminderTime.setText(context.getString(R.string.task_reminder) + &quot;: &quot; + task.getFormattedReminderTime());"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/example/todolist/TaskAdapter.java"
            line="92"
            column="45"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Task Title&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Task Title&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Orta&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Orta&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="60"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Pending&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Pending&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="77"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Task description goes here...&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Task description goes here...&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;01/01/2024 12:00&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;01/01/2024 12:00&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Deadline: Yoxdur&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Deadline: Yoxdur&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="123"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Reminder: 01/01/2024 11:00&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Reminder: 01/01/2024 11:00&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/task_item.xml"
            line="152"
            column="17"/>
    </issue>

</issues>
