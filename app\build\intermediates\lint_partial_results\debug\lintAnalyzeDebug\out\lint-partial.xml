<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.3" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="ScheduleExactAlarm">
        <entry
            name="ChecksExactAlarmPermission"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.MANAGE_APP_ALL_FILES_ACCESS_PERMISSION (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/todolist/MainActivity.java"
                            line="679"
                            column="33"
                            startOffset="29284"
                            endLine="679"
                            endColumn="99"
                            endOffset="29350"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.todolist.LoginActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.background_completed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="53"
            column="12"
            startOffset="2252"
            endLine="53"
            endColumn="39"
            endOffset="2279"/>
        <location id="R.color.background_in_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="52"
            column="12"
            startOffset="2195"
            endLine="52"
            endColumn="41"
            endOffset="2224"/>
        <location id="R.color.background_pending"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2142"
            endLine="51"
            endColumn="37"
            endOffset="2167"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="1707"
            endLine="42"
            endColumn="24"
            endOffset="1719"/>
        <location id="R.color.error_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="37"
            column="12"
            startOffset="1509"
            endLine="37"
            endColumn="27"
            endOffset="1524"/>
        <location id="R.color.error_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="1552"
            endLine="38"
            endColumn="28"
            endOffset="1568"/>
        <location id="R.color.neutral_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="236"
            endLine="7"
            endColumn="30"
            endOffset="254"/>
        <location id="R.color.neutral_300"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="282"
            endLine="8"
            endColumn="30"
            endOffset="300"/>
        <location id="R.color.neutral_400"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="328"
            endLine="9"
            endColumn="30"
            endOffset="346"/>
        <location id="R.color.neutral_800"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="512"
            endLine="13"
            endColumn="30"
            endOffset="530"/>
        <location id="R.color.neutral_950"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="604"
            endLine="15"
            endColumn="30"
            endOffset="622"/>
        <location id="R.color.primary_100"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="739"
            endLine="19"
            endColumn="30"
            endOffset="757"/>
        <location id="R.color.primary_300"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="831"
            endLine="21"
            endColumn="30"
            endOffset="849"/>
        <location id="R.color.primary_400"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="877"
            endLine="22"
            endColumn="30"
            endOffset="895"/>
        <location id="R.color.primary_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="1015"
            endLine="25"
            endColumn="30"
            endOffset="1033"/>
        <location id="R.color.primary_800"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1061"
            endLine="26"
            endColumn="30"
            endOffset="1079"/>
        <location id="R.color.primary_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1107"
            endLine="27"
            endColumn="30"
            endOffset="1125"/>
        <location id="R.color.primary_950"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1153"
            endLine="28"
            endColumn="30"
            endOffset="1171"/>
        <location id="R.color.success_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="32"
            column="12"
            startOffset="1280"
            endLine="32"
            endColumn="30"
            endOffset="1298"/>
        <location id="R.color.warning_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1372"
            endLine="34"
            endColumn="29"
            endOffset="1389"/>
        <location id="R.color.warning_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1417"
            endLine="35"
            endColumn="30"
            endOffset="1435"/>
        <location id="R.color.white"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="1749"
            endLine="43"
            endColumn="24"
            endOffset="1761"/>
        <location id="R.drawable.fab_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fab_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="15"
            endColumn="10"
            endOffset="457"/>
        <location id="R.drawable.gradient_accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_accent.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="327"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1702"/>
        <location id="R.drawable.modern_card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/modern_card_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="14"
            endColumn="9"
            endOffset="449"/>
        <location id="R.drawable.task_item_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/task_item_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="342"/>
        <location id="R.mipmap.ic_launcher_round"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp"/>
        <location id="R.string.no_tasks"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1479"
            endLine="28"
            endColumn="28"
            endOffset="1494"/>
        <location id="R.string.sign_out"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="5173"
            endLine="100"
            endColumn="28"
            endOffset="5188"/>
        <location id="R.string.signed_in_as"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="5216"
            endLine="101"
            endColumn="32"
            endOffset="5235"/>
        <location id="R.string.sync_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="103"
            column="13"
            startOffset="5340"
            endLine="103"
            endColumn="31"
            endOffset="5358"/>
        <location id="R.string.sync_now"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="99"
            column="13"
            startOffset="5113"
            endLine="99"
            endColumn="28"
            endOffset="5128"/>
        <location id="R.string.sync_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="102"
            column="13"
            startOffset="5273"
            endLine="102"
            endColumn="32"
            endOffset="5292"/>
        <location id="R.string.task_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="118"
            endLine="4"
            endColumn="29"
            endOffset="134"/>
        <entry
            name="model"
            string="anim[fade_in(U),scale_in(U),slide_in_right(U),slide_out_left(U)],array[category_options(U)],attr[colorOnSurface(R),actionBarSize(R),selectableItemBackgroundBorderless(R)],color[card_background(U),divider_color(U),accent_color(U),secondary_color(U),primary_dark_color(U),primary_color(U),text_primary(U),text_secondary(U),neutral_100(U),primary_600(U),neutral_0(U),neutral_700(U),primary_500(U),primary_200(U),surface_color(U),status_completed(U),status_pending(U),status_in_progress(U),error_color(U),warning_color(U),success_color(U),text_hint(U),neutral_900(U),neutral_500(U),neutral_600(U),neutral_50(U),neutral_200(D),neutral_300(D),neutral_400(D),neutral_800(D),neutral_950(D),primary_50(U),primary_100(D),primary_300(D),primary_400(D),primary_700(D),primary_800(D),primary_900(D),primary_950(D),success_50(U),success_500(D),success_600(U),warning_50(D),warning_500(D),warning_600(U),error_50(D),error_500(D),error_600(U),black(D),white(D),background_pending(D),background_in_progress(D),background_completed(D)],drawable[edit_text_background(U),fab_background(D),google_sign_in_button(U),gradient_accent(D),gradient_primary(U),ic_account(U),ic_add(U),ic_app_logo(U),ic_calendar(U),ic_empty_tasks(U),ic_filter(U),ic_google(U),ic_launcher_background(D),ic_launcher_foreground(D),ic_launcher_foreground_1(E),ic_notification(U),ic_sort(U),ic_statistics(U),modern_card_background(D),priority_background(U),status_background(U),task_item_background(D)],id[btnGoogleSignIn(U),btnContinueOffline(U),main(U),toolbar(U),filterButton(U),tasksCountTextView(U),swipeRefreshLayout(U),tasksListView(U),emptyStateView(U),fabAddTask(U),main_statistics(U),totalTasksTextView(U),completionRateTextView(U),completedTasksTextView(U),pendingTasksTextView(U),inProgressTasksTextView(U),highPriorityTasksTextView(U),mediumPriorityTasksTextView(U),lowPriorityTasksTextView(U),editTextTitle(U),editTextDescription(U),spinnerStatus(U),spinnerPriority(U),spinnerCategory(U),editTextDeadline(U),imageViewCalendar(U),editTextReminder(U),imageViewReminderCalendar(U),buttonCancel(U),buttonAdd(U),spinnerFilterStatus(U),editTextSearch(U),buttonClearFilter(U),buttonApplyFilter(U),radioGroupSort(U),radioSortDateCreated(U),radioSortPriority(U),radioSortStatus(U),radioSortTitle(U),radioSortDeadline(U),radioSortCategory(U),buttonCancelSort(U),buttonApplySort(U),buttonAddFirstTask(U),taskCompletedCheckbox(U),priorityIndicator(U),taskTitle(U),taskPriority(U),taskStatus(U),taskDescription(U),taskDate(U),taskDeadline(U),reminderLayout(U),taskReminderTime(U),action_account(D),action_statistics(U),action_sort(U),action_backup(U),action_restore(U),action_clear_all(U)],layout[activity_login(U),activity_main(U),empty_state(U),activity_statistics(U),dialog_add_task(U),dialog_filter(U),dialog_sort(U),task_item(U)],menu[main_menu(U)],mipmap[ic_launcher(U),ic_launcher_round(D)],string[app_name(U),login_subtitle(U),sign_in_title(U),sign_in_description(U),sign_in_with_google(U),continue_without_account(U),login_footer(U),filter(U),total_tasks(U),add_task_button(U),overall_statistics(U),total_tasks_label(U),completion_rate(U),status_statistics(U),status_completed(U),status_pending(U),status_in_progress(U),priority_statistics(U),priority_high(U),priority_medium(U),priority_low(U),add_new_task(U),task_title(U),task_description(U),task_status(U),task_priority(U),task_category(U),task_deadline(U),select_deadline(U),task_reminder(U),select_reminder_time(U),cancel(U),filter_tasks(U),filter_by_status(U),search_tasks(U),search_hint(U),clear_filter(U),apply_filter(U),sort_tasks(U),sort_by_date_created(U),sort_by_priority(U),sort_by_status(U),sort_by_title(U),sort_by_deadline(U),sort_by_category(U),no_tasks_title(U),no_tasks_subtitle(U),add_first_task(U),account_menu(U),statistics(U),backup_export(U),backup_import(U),clear_all_tasks(U),category_work(U),category_personal(U),category_shopping(U),category_health(U),category_education(U),category_other(U),task_hint(D),add_task(U),edit_task(U),delete_task(U),task_updated(U),task_deleted(U),no_tasks(D),refresh_tasks(U),status_all(U),error_empty_title(U),error_empty_description(U),delete_confirmation(U),clear_all_confirmation(U),yes(U),no(U),task_added(U),all_tasks_cleared(U),sync_now(D),sign_out(D),signed_in_as(D),sync_success(D),sync_failed(D),default_web_client_id(U)],style[Theme_Todolist(U),Widget_AppCompat_Button_Borderless(R),ThemeOverlay_AppCompat_Dark_ActionBar(R),ThemeOverlay_AppCompat_Light(R),Base_Theme_Todolist(U),Theme_AppCompat_Light_NoActionBar(R)];4^cf^d0^d1^d2^d3^d4,3d^8^9,3e^a^b,40^b^a,41^c^d,42^5,43^5,44^5,45^5,46^5,47^5,4a^4b,4c^5,4d^5,4e^5,4f^8^9,52^8^9,8f^41^44^9a^9b^8^9c^e^9d^f^3f^48^9e^9f^ed^a0,90^10^11^ee^41^6^ef^9a^12^7^a1^47^a2^13^91^a3^43^14^15,91^16^46^1d^c7^e^c8^f^a^c9,92^16^8^a4^e^a5^f^a6^a7^a8^17^a9^18^aa^19^ab^ac^1a^ad^1b^ae^1c,93^af^3d^b0^b1^b2^e^b3^b4^b5^b6^45^d^b7^b8^b9^a3,94^ba^bb^bc^3d^bd^be^bf,95^c0^c1^c2^c3^c4^c5^c6^b9^bf,96^12^11^1e^50^51^13^b5^45^1f^20^b7^4c,97^42^ca^4e^cb^4d^c0^cc^cd^ce,ec^f0,f0^f1^d^c^a^16^e^f^1d;;;"/>
    </map>

</incidents>
