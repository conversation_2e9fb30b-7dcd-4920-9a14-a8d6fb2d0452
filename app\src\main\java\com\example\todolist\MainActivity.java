package com.example.todolist;
import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.appcompat.widget.Toolbar;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.firebase.auth.FirebaseUser;

import android.Manifest;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Environment;
import android.provider.Settings;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import android.os.Build;


public class MainActivity extends AppCompatActivity implements TaskAdapter.OnTaskActionListener { // Implement interface

    private Toolbar toolbar;
    private ImageButton filterButton;
    private FloatingActionButton fabAddTask;
    private ListView tasksListView;
    private TextView tasksCountTextView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private View emptyStateView;
    private ArrayList<Task> tasks;
    private TaskAdapter adapter;
    private String currentFilter = "Hamısı";
    private String currentSearchText = "";
    private String currentSortOrder = "Yaradılma Tarixinə Görə"; // Default sort order
    private boolean isDataLoaded = false;

    // Request codes for permissions and file pickers
    private static final int REQUEST_PERMISSIONS = 1;
    private static final int PICK_FILE_REQUEST_CODE = 2;

    private ActivityResultLauncher<Intent> pickFileLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            EdgeToEdge.enable(this);
            setContentView(R.layout.activity_main);

            ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
                Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
                v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
                return insets;
            });

            // Initialize the ActivityResultLauncher for file picking
            pickFileLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                    result -> {
                        if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                            Uri uri = result.getData().getData();
                            if (uri != null) {
                                handleImportResult(uri);
                            }
                        } else {
                            Toast.makeText(this, "Fayl seçilmədi", Toast.LENGTH_SHORT).show();
                        }
                    });

            initializeViews();
            setupToolbar();
            setupTasksList();
            applySort(); // Apply default sort order on startup
            setupClickListeners();
            setupNotifications();
            checkOverdueTasks();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Proqram başladılarkən xəta: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        fabAddTask = findViewById(R.id.fabAddTask);
        filterButton = findViewById(R.id.filterButton);
        tasksListView = findViewById(R.id.tasksListView);
        tasksCountTextView = findViewById(R.id.tasksCountTextView);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        emptyStateView = findViewById(R.id.emptyStateView);

        // Setup empty state button
        Button addFirstTaskButton = emptyStateView.findViewById(R.id.buttonAddFirstTask);
        addFirstTaskButton.setOnClickListener(v -> showTaskDialog(null)); // Use unified dialog
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayShowTitleEnabled(false); // Disable default title
        }
    }

    private void setupTasksList() {
        tasks = new ArrayList<>();
        loadTasks(); // Load synchronously for now
        adapter = new TaskAdapter(this, tasks, this); // Pass 'this' as listener
        tasksListView.setAdapter(adapter);
        updateTasksCount();
        isDataLoaded = true;

        // Setup SwipeRefreshLayout
        swipeRefreshLayout.setColorSchemeResources(
                R.color.primary_color,
                R.color.accent_color,
                R.color.secondary_color
        );
        swipeRefreshLayout.setOnRefreshListener(this::refreshTasks);
    }

    private void setupClickListeners() {
        // FAB click listener with animation
        fabAddTask.setOnClickListener(v -> {
            Animation scaleIn = AnimationUtils.loadAnimation(this, R.anim.scale_in);
            v.startAnimation(scaleIn);
            showTaskDialog(null); // Use unified dialog for adding
        });

        // Filter button click listener
        filterButton.setOnClickListener(v -> showFilterDialog());

        // Remove old item click listeners as they are now handled by adapter's interface
        tasksListView.setOnItemClickListener(null);
        tasksListView.setOnItemLongClickListener(null);
    }

    // Unified method for adding and editing tasks
    private void showTaskDialog(Task taskToEdit) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        EditText titleEditText = dialogView.findViewById(R.id.editTextTitle);
        EditText descriptionEditText = dialogView.findViewById(R.id.editTextDescription);
        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerStatus);
        Spinner prioritySpinner = dialogView.findViewById(R.id.spinnerPriority);
        Spinner categorySpinner = dialogView.findViewById(R.id.spinnerCategory);
        EditText deadlineEditText = dialogView.findViewById(R.id.editTextDeadline);
        ImageView calendarImageView = dialogView.findViewById(R.id.imageViewCalendar);
        EditText reminderEditText = dialogView.findViewById(R.id.editTextReminder); // New reminder EditText
        ImageView reminderCalendarImageView = dialogView.findViewById(R.id.imageViewReminderCalendar); // New reminder ImageView
        Button actionButton = dialogView.findViewById(R.id.buttonAdd);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancel);

        final Calendar calendar = Calendar.getInstance();
        final long[] selectedDeadline = {0};
        final long[] selectedReminderTime = {0}; // New for reminder time

        boolean isEditMode = (taskToEdit != null);

        if (isEditMode) {
            titleEditText.setText(taskToEdit.getTitle());
            descriptionEditText.setText(taskToEdit.getDescription());
            if (taskToEdit.hasDeadline()) {
                calendar.setTimeInMillis(taskToEdit.getDeadline());
                deadlineEditText.setText(taskToEdit.getFormattedDeadline());
                selectedDeadline[0] = taskToEdit.getDeadline();
            }
            if (taskToEdit.hasReminder()) { // Pre-fill reminder time
                calendar.setTimeInMillis(taskToEdit.getReminderTime());
                reminderEditText.setText(taskToEdit.getFormattedReminderTime());
                selectedReminderTime[0] = taskToEdit.getReminderTime();
            }
            actionButton.setText(getString(R.string.edit_task));
        } else {
            actionButton.setText(getString(R.string.add_task));
            prioritySpinner.setSelection(1);
            categorySpinner.setSelection(Arrays.asList(getResources().getStringArray(R.array.category_options)).indexOf(Task.CATEGORY_OTHER));
        }

        // Date and Time Picker for Deadline
        View.OnClickListener deadlineDateTimePickerClickListener = v -> {
            new DatePickerDialog(MainActivity.this, (view, year, month, dayOfMonth) -> {
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);

                new TimePickerDialog(MainActivity.this, (timeView, hourOfDay, minute) -> {
                    calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                    calendar.set(Calendar.MINUTE, minute);
                    calendar.set(Calendar.SECOND, 0);
                    selectedDeadline[0] = calendar.getTimeInMillis();
                    Task tempTask = new Task();
                    tempTask.setDeadline(selectedDeadline[0]);
                    deadlineEditText.setText(tempTask.getFormattedDeadline());
                }, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), true).show();
            }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)).show();
        };

        deadlineEditText.setOnClickListener(deadlineDateTimePickerClickListener);
        calendarImageView.setOnClickListener(deadlineDateTimePickerClickListener);

        // Date and Time Picker for Reminder
        View.OnClickListener reminderDateTimePickerClickListener = v -> {
            new DatePickerDialog(MainActivity.this, (view, year, month, dayOfMonth) -> {
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);

                new TimePickerDialog(MainActivity.this, (timeView, hourOfDay, minute) -> {
                    calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                    calendar.set(Calendar.MINUTE, minute);
                    calendar.set(Calendar.SECOND, 0);
                    selectedReminderTime[0] = calendar.getTimeInMillis();
                    Task tempTask = new Task();
                    tempTask.setReminderTime(selectedReminderTime[0]);
                    reminderEditText.setText(tempTask.getFormattedReminderTime());
                }, calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE), true).show();
            }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)).show();
        };

        reminderEditText.setOnClickListener(reminderDateTimePickerClickListener);
        reminderCalendarImageView.setOnClickListener(reminderDateTimePickerClickListener);

        // Setup status spinner
        String[] statusOptions = {Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusOptions);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);
        if (isEditMode) {
            int statusPosition = Arrays.asList(statusOptions).indexOf(taskToEdit.getStatus());
            if (statusPosition >= 0) {
                statusSpinner.setSelection(statusPosition);
            }
        }

        // Setup priority spinner
        String[] priorityOptions = {Task.PRIORITY_HIGH, Task.PRIORITY_MEDIUM, Task.PRIORITY_LOW};
        ArrayAdapter<String> priorityAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, priorityOptions);
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        prioritySpinner.setAdapter(priorityAdapter);
        if (isEditMode) {
            int priorityPosition = Arrays.asList(priorityOptions).indexOf(taskToEdit.getPriority());
            if (priorityPosition >= 0) {
                prioritySpinner.setSelection(priorityPosition);
            }
        }

        // Setup category spinner
        String[] categoryOptions = {Task.CATEGORY_WORK, Task.CATEGORY_PERSONAL, Task.CATEGORY_SHOPPING, Task.CATEGORY_HEALTH, Task.CATEGORY_EDUCATION, Task.CATEGORY_OTHER};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, categoryOptions);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(categoryAdapter);
        if (isEditMode) {
            int categoryPosition = Arrays.asList(categoryOptions).indexOf(taskToEdit.getCategory());
            if (categoryPosition >= 0) {
                categorySpinner.setSelection(categoryPosition);
            }
        }

        AlertDialog dialog = builder.create();

        actionButton.setOnClickListener(v -> {
            String title = titleEditText.getText().toString().trim();
            String description = descriptionEditText.getText().toString().trim();
            String status = statusSpinner.getSelectedItem().toString();
            String priority = prioritySpinner.getSelectedItem().toString();
            String category = categorySpinner.getSelectedItem().toString();

            if (title.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_title), Toast.LENGTH_SHORT).show();
                return;
            }

            if (description.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_description), Toast.LENGTH_SHORT).show();
                return;
            }

            if (isEditMode) {
                // Cancel existing reminder if reminder time changed or removed
                if (taskToEdit.hasReminder() && taskToEdit.getReminderTime() != selectedReminderTime[0]) {
                    NotificationHelper.cancelTaskReminder(this, taskToEdit.hashCode());
                }

                taskToEdit.setTitle(title);
                taskToEdit.setDescription(description);
                taskToEdit.setStatus(status);
                taskToEdit.setPriority(priority);
                taskToEdit.setCategory(category);
                taskToEdit.setDeadline(selectedDeadline[0]);
                taskToEdit.setReminderTime(selectedReminderTime[0]); // Set reminder time
                adapter.updateTask(taskToEdit, taskToEdit);
                Toast.makeText(this, getString(R.string.task_updated), Toast.LENGTH_SHORT).show();

                // Schedule new reminder if set
                if (taskToEdit.hasReminder()) {
                    NotificationHelper.scheduleTaskReminder(this, taskToEdit, taskToEdit.getReminderTime(), taskToEdit.hashCode());
                }
            } else {
                Task newTask = new Task(title, description, status, priority);
                newTask.setDeadline(selectedDeadline[0]);
                newTask.setCategory(category);
                newTask.setReminderTime(selectedReminderTime[0]); // Set reminder time
                adapter.addTask(newTask);
                Animation slideIn = AnimationUtils.loadAnimation(this, R.anim.slide_in_right);
                tasksListView.startAnimation(slideIn);
                Toast.makeText(this, getString(R.string.task_added), Toast.LENGTH_SHORT).show();

                // Schedule reminder for new task if set
                if (newTask.hasReminder()) {
                    NotificationHelper.scheduleTaskReminder(this, newTask, newTask.getReminderTime(), newTask.hashCode());
                }
            }
            saveTasks();
            applyFilters();
            updateTasksCount();
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    // Implement TaskAdapter.OnTaskActionListener methods
    @Override
    public void onTaskStatusChanged(Task task, boolean isCompleted) {
        task.setStatus(isCompleted ? Task.STATUS_COMPLETED : Task.STATUS_PENDING);
        // Cancel reminder if task is completed
        if (isCompleted && task.hasReminder()) {
            NotificationHelper.cancelTaskReminder(this, task.hashCode());
        }
        saveTasks();
        applyFilters();
        Toast.makeText(this, "Tapşırıq statusu dəyişdirildi: " + task.getTitle(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onTaskEdit(Task task) {
        showTaskDialog(task);
    }

    @Override
    public void onTaskDelete(Task task) {
        new AlertDialog.Builder(this)
                .setTitle(getString(R.string.delete_task))
                .setMessage(getString(R.string.delete_confirmation))
                .setPositiveButton(getString(R.string.yes), (dialog, which) -> {
                    // Cancel reminder before deleting task
                    if (task.hasReminder()) {
                        NotificationHelper.cancelTaskReminder(this, task.hashCode());
                    }
                    adapter.removeTask(task);
                    Animation slideOut = AnimationUtils.loadAnimation(this, R.anim.slide_out_left);
                    tasksListView.startAnimation(slideOut);
                    saveTasks();
                    updateTasksCount();
                    Toast.makeText(MainActivity.this, getString(R.string.task_deleted), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.no), null)
                .show();
    }

    private void showFilterDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_filter, null);
        builder.setView(dialogView);

        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerFilterStatus);
        EditText searchEditText = dialogView.findViewById(R.id.editTextSearch);
        Button applyButton = dialogView.findViewById(R.id.buttonApplyFilter);
        Button clearButton = dialogView.findViewById(R.id.buttonClearFilter);

        // Setup status spinner
        String[] filterOptions = {getString(R.string.status_all), Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> filterAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, filterOptions);
        filterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(filterAdapter);

        // Set current filter
        int filterPosition = Arrays.asList(filterOptions).indexOf(currentFilter);
        if (filterPosition >= 0) {
            statusSpinner.setSelection(filterPosition);
        }

        // Set current search text
        searchEditText.setText(currentSearchText);

        AlertDialog dialog = builder.create();

        applyButton.setOnClickListener(v -> {
            currentFilter = statusSpinner.getSelectedItem().toString();
            currentSearchText = searchEditText.getText().toString().trim();
            applyFilters();
            dialog.dismiss();
        });

        clearButton.setOnClickListener(v -> {
            currentFilter = getString(R.string.status_all);
            currentSearchText = "";
            adapter.resetFilter();
            updateTasksCount();
            dialog.dismiss();
        });

        dialog.show();
    }

    public void applyFilters() {
        if (!currentSearchText.isEmpty()) {
            adapter.filterByTitle(currentSearchText);
        } else if (!currentFilter.equals(getString(R.string.status_all))) {
            adapter.filterByStatus(currentFilter);
        } else {
            // Default filter: show all tasks except completed ones
            adapter.filterByStatusExcludingCompleted();
        }
        updateTasksCount();
    }

    private void updateTasksCount() {
        int totalTasks = tasks.size();
        tasksCountTextView.setText(getString(R.string.total_tasks, totalTasks));

        // Show/hide empty state with animation
        if (totalTasks == 0) {
            if (emptyStateView.getVisibility() != View.VISIBLE) {
                Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
                emptyStateView.startAnimation(fadeIn);
                emptyStateView.setVisibility(View.VISIBLE);
                swipeRefreshLayout.setVisibility(View.GONE);
            }
        } else {
            if (swipeRefreshLayout.getVisibility() != View.VISIBLE) {
                Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
                swipeRefreshLayout.startAnimation(fadeIn);
                emptyStateView.setVisibility(View.GONE);
                swipeRefreshLayout.setVisibility(View.VISIBLE);
            }
        }
    }

    private void refreshTasks() {
        // Simulate refresh delay
        new android.os.Handler().postDelayed(() -> {
            // Reload tasks from storage
            loadTasks();
            adapter.notifyDataSetChanged();
            updateTasksCount();
            applyFilters();
            swipeRefreshLayout.setRefreshing(false);
            Toast.makeText(this, getString(R.string.refresh_tasks), Toast.LENGTH_SHORT).show();
        }, 1000);
    }

    public void saveTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(tasks);
        editor.putString("task_list", json);
        editor.apply();
    }

    private void loadTasks() {
        try {
            SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
            Gson gson = new Gson();
            String json = sharedPreferences.getString("task_list", null);

            if (json != null && !json.isEmpty()) {
                Type type = new TypeToken<ArrayList<Task>>() {}.getType();
                ArrayList<Task> loadedTasks = gson.fromJson(json, type);
                if (loadedTasks != null) {
                    tasks = loadedTasks;
                } else {
                    tasks = new ArrayList<>();
                }
            } else {
                tasks = new ArrayList<>();
            }
        } catch (Exception e) {
            e.printStackTrace();
            tasks = new ArrayList<>();
            Toast.makeText(this, "Tapşırıqlar yüklənərkən xəta", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_statistics) {
            Intent intent = new Intent(this, StatisticsActivity.class);
            startActivity(intent);
            return true;
        } else if (id == R.id.action_sort) {
            showSortDialog();
            return true;
        } else if (id == R.id.action_backup) {
            checkAndRequestPermissions(true); // true for backup
            return true;
        } else if (id == R.id.action_restore) {
            checkAndRequestPermissions(false); // false for restore
            return true;
        } else if (id == R.id.action_clear_all) {
            new AlertDialog.Builder(this)
                    .setTitle(getString(R.string.clear_all_tasks))
                    .setMessage(getString(R.string.clear_all_confirmation))
                    .setPositiveButton(getString(R.string.yes), (dialog, which) -> {
                        tasks.clear();
                        adapter.notifyDataSetChanged();
                        saveTasks();
                        updateTasksCount();
                        Toast.makeText(MainActivity.this, getString(R.string.all_tasks_cleared), Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton(getString(R.string.no), null)
                    .show();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void setupNotifications() {
        NotificationHelper.createNotificationChannel(this);
    }

    private void checkOverdueTasks() {
        if (tasks == null || tasks.isEmpty()) {
            return;
        }

        try {
            int notificationId = 1000;
            for (Task task : tasks) {
                if (task != null && task.hasDeadline()) {
                    if (task.isOverdue()) {
                        NotificationHelper.showOverdueTaskNotification(this, task, notificationId++);
                    } else if (task.isDueSoon()) {
                        NotificationHelper.showTaskReminderNotification(this, task.getTitle(), task.getDescription(), notificationId++);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showSortDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_sort, null);
        builder.setView(dialogView);

        RadioGroup radioGroupSort = dialogView.findViewById(R.id.radioGroupSort);
        Button applyButton = dialogView.findViewById(R.id.buttonApplySort);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancelSort);

        // Set current sort order
        switch (currentSortOrder) {
            case "Yaradılma Tarixinə Görə":
                radioGroupSort.check(R.id.radioSortDateCreated);
                break;
            case "Prioritetə Görə":
                radioGroupSort.check(R.id.radioSortPriority);
                break;
            case "Statusa Görə":
                radioGroupSort.check(R.id.radioSortStatus);
                break;
            case "Başlığa Görə":
                radioGroupSort.check(R.id.radioSortTitle);
                break;
            case "Deadline-a Görə":
                radioGroupSort.check(R.id.radioSortDeadline);
                break;
            case "Kateqoriyaya Görə":
                radioGroupSort.check(R.id.radioSortCategory);
                break;
        }

        AlertDialog dialog = builder.create();

        applyButton.setOnClickListener(v -> {
            int selectedId = radioGroupSort.getCheckedRadioButtonId();
            if (selectedId != -1) {
                currentSortOrder = ((RadioButton) dialogView.findViewById(selectedId)).getText().toString();
                applySort();
            }
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    private void applySort() {
        switch (currentSortOrder) {
            case "Yaradılma Tarixinə Görə":
                adapter.sortByDateCreated();
                break;
            case "Prioritetə Görə":
                adapter.sortByPriority();
                break;
            case "Statusa Görə":
                adapter.sortByStatus();
                break;
            case "Başlığa Görə":
                adapter.sortByTitle();
                break;
            case "Deadline-a Görə":
                adapter.sortByDeadline();
                break;
            case "Kateqoriyaya Görə":
                adapter.sortByCategory();
                break;
        }
        adapter.notifyDataSetChanged();
    }

    private void handleImportResult(Uri uri) {
        importTasksFromFile(uri);
    }

    private void checkAndRequestPermissions(boolean isBackup) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()) {
                if (isBackup) {
                    exportTasksToFile();
                } else {
                    openFilePicker();
                }
            } else {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                Uri uri = Uri.fromParts("package", getPackageName(), null);
                intent.setData(uri);
                startActivity(intent);
                Toast.makeText(this, "Fayl icazəsi tələb olunur. Zəhmət olmasa icazə verin.", Toast.LENGTH_LONG).show();
            }
        } else {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_PERMISSIONS);
            } else {
                if (isBackup) {
                    exportTasksToFile();
                } else {
                    openFilePicker();
                }
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "İcazə verildi", Toast.LENGTH_SHORT).show();
                // Re-check permissions to determine if it's backup or restore
                // This is a simplified approach; in a real app, you might pass a flag
                // or use a more sophisticated state management.
                // For now, we'll assume the user will re-attempt the action.
            } else {
                Toast.makeText(this, "İcazə rədd edildi. Fayl əməliyyatları mümkün deyil.", Toast.LENGTH_LONG).show();
            }
        }
    }

    private void exportTasksToFile() {
        if (tasks.isEmpty()) {
            Toast.makeText(this, "Export etmək üçün tapşırıq yoxdur.", Toast.LENGTH_SHORT).show();
            return;
        }

        File directory = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS), "TodolistBackup");
        if (!directory.exists()) {
            directory.mkdirs();
        }

        File file = new File(directory, "tasks_backup.json");
        try (FileOutputStream fos = new FileOutputStream(file);
             OutputStreamWriter osw = new OutputStreamWriter(fos)) {
            Gson gson = new Gson();
            String json = gson.toJson(tasks);
            osw.write(json);
            Toast.makeText(this, "Tapşırıqlar uğurla export edildi: " + file.getAbsolutePath(), Toast.LENGTH_LONG).show();
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "Tapşırıqlar export edilərkən xəta: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void openFilePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("application/json");
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        pickFileLauncher.launch(intent);
    }

    private void importTasksFromFile(Uri uri) {
        try (InputStreamReader isr = new InputStreamReader(getContentResolver().openInputStream(uri));
             BufferedReader reader = new BufferedReader(isr)) {
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            String json = builder.toString();

            Gson gson = new Gson();
            Type type = new TypeToken<ArrayList<Task>>() {}.getType();
            ArrayList<Task> importedTasks = gson.fromJson(json, type);

            if (importedTasks != null && !importedTasks.isEmpty()) {
                tasks.clear();
                tasks.addAll(importedTasks);
                adapter.resetFilter();
                saveTasks();
                updateTasksCount();
                Toast.makeText(this, "Tapşırıqlar uğurla import edildi!", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Import ediləcək tapşırıq tapılmadı və ya fayl boşdur.", Toast.LENGTH_SHORT).show();
            }
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "Tapşırıqlar import edilərkən xəta: " + e.getMessage(), Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Fayl oxunarkən xəta: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
}
