{"logs": [{"outputFile": "com.example.todolist.app-mergeDebugResources-36:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d8ec8ac963a493a193201cc8ee075ff5\\transformed\\material-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1060,1123,1214,1281,1340,1430,1493,1558,1622,1691,1753,1807,1922,1980,2041,2095,2168,2295,2381,2463,2562,2647,2731,2864,2939,3015,3148,3234,3315,3369,3421,3487,3560,3640,3711,3791,3862,3938,4017,4086,4193,4289,4367,4462,4558,4632,4707,4806,4857,4939,5006,5093,5183,5245,5309,5372,5439,5541,5646,5743,5845,5903,5959,6037,6123,6198", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "263,337,409,491,597,695,794,914,998,1055,1118,1209,1276,1335,1425,1488,1553,1617,1686,1748,1802,1917,1975,2036,2090,2163,2290,2376,2458,2557,2642,2726,2859,2934,3010,3143,3229,3310,3364,3416,3482,3555,3635,3706,3786,3857,3933,4012,4081,4188,4284,4362,4457,4553,4627,4702,4801,4852,4934,5001,5088,5178,5240,5304,5367,5434,5536,5641,5738,5840,5898,5954,6032,6118,6193,6266"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,3335,4151,4250,4370,6731,6788,6851,7253,7320,7379,7469,7532,7597,7661,7730,7792,7846,7961,8019,8080,8134,8207,8334,8420,8502,8601,8686,8770,8903,8978,9054,9187,9273,9354,9408,9460,9526,9599,9679,9750,9830,9901,9977,10056,10125,10232,10328,10406,10501,10597,10671,10746,10845,10896,10978,11045,11132,11222,11284,11348,11411,11478,11580,11685,11782,11884,11942,11998,12157,12243,12318", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "313,3070,3142,3224,3330,3428,4245,4365,4449,6783,6846,6937,7315,7374,7464,7527,7592,7656,7725,7787,7841,7956,8014,8075,8129,8202,8329,8415,8497,8596,8681,8765,8898,8973,9049,9182,9268,9349,9403,9455,9521,9594,9674,9745,9825,9896,9972,10051,10120,10227,10323,10401,10496,10592,10666,10741,10840,10891,10973,11040,11127,11217,11279,11343,11406,11473,11575,11680,11777,11879,11937,11993,12071,12238,12313,12386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\47acb2d51539ebc7e1b6659adb48b4bc\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,12076", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,12152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\94b25b2e0232f11eacc589061e7c8810\\transformed\\core-1.13.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3433,3527,3630,3727,3829,3931,4029,12391", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3522,3625,3722,3824,3926,4024,4146,12487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ccc6704ec40174d3a5896d53e2b7c03a\\transformed\\browser-1.4.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6630,6942,7043,7152", "endColumns": "100,100,108,100", "endOffsets": "6726,7038,7147,7248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1351b8c1d00e574e81c77e445d78a1cb\\transformed\\play-services-base-18.0.1\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4454,4562,4728,4853,4965,5103,5225,5336,5583,5731,5839,6003,6128,6271,6421,6482,6548", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "4557,4723,4848,4960,5098,5220,5331,5431,5726,5834,5998,6123,6266,6416,6477,6543,6625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\edf9bf37f6d93d447fb63ea57318ab6f\\transformed\\play-services-basement-18.2.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5436", "endColumns": "146", "endOffsets": "5578"}}]}]}