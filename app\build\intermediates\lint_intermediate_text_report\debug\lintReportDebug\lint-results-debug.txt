C:\Users\<USER>\Music\remote\todolist\app\build.gradle:13: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTarget<PERSON>pi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.7.3 is available: 8.11.0 [AndroidGradlePluginVersion]
agp = "8.7.3"
      ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.7.3 is available: 8.11.0 [AndroidGradlePluginVersion]
agp = "8.7.3"
      ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.7.3 is available: 8.11.0 [AndroidGradlePluginVersion]
agp = "8.7.3"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\Music\remote\todolist\app\build.gradle:43: Warning: A newer version of com.google.firebase:firebase-bom than 32.7.0 is available: 33.16.0 [GradleDependency]
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:46: Warning: A newer version of com.google.android.gms:play-services-auth than 20.7.0 is available: 21.3.0 [GradleDependency]
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:8: Warning: A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2"
           ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:8: Warning: A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2"
           ~~~~~~~
C:\Users\<USER>\Music\remote\todolist\gradle\libs.versions.toml:8: Warning: A newer version of androidx.activity:activity than 1.8.2 is available: 1.10.1 [GradleDependency]
activity = "1.8.2"
           ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_login.xml:17: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@android:color/white" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_main.xml:55: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                        android:tint="@color/neutral_0"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:123: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="@color/primary_color"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:167: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="@color/primary_color"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\empty_state.xml:14: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@color/text_hint"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\NotificationHelper.java:96: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:131: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_main.xml:8: Warning: Possible overdraw: Root element paints background @color/neutral_100 with a theme that also paints a background (inferred theme is @style/Theme.Todolist) [Overdraw]
    android:background="@color/neutral_100"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_statistics.xml:8: Warning: Possible overdraw: Root element paints background @color/surface_color with a theme that also paints a background (inferred theme is @style/Theme.Todolist) [Overdraw]
    android:background="@color/surface_color"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\empty_state.xml:8: Warning: Possible overdraw: Root element paints background @color/surface_color with a theme that also paints a background (inferred theme is @style/Theme.Todolist) [Overdraw]
    android:background="@color/surface_color">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.neutral_200 appears to be unused [UnusedResources]
    <color name="neutral_200">#e5e5e5</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.neutral_300 appears to be unused [UnusedResources]
    <color name="neutral_300">#d4d4d4</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.neutral_400 appears to be unused [UnusedResources]
    <color name="neutral_400">#a3a3a3</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.neutral_800 appears to be unused [UnusedResources]
    <color name="neutral_800">#262626</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.neutral_950 appears to be unused [UnusedResources]
    <color name="neutral_950">#0a0a0a</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.primary_100 appears to be unused [UnusedResources]
    <color name="primary_100">#dbeafe</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.primary_300 appears to be unused [UnusedResources]
    <color name="primary_300">#93c5fd</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.primary_400 appears to be unused [UnusedResources]
    <color name="primary_400">#60a5fa</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.primary_700 appears to be unused [UnusedResources]
    <color name="primary_700">#1d4ed8</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.primary_800 appears to be unused [UnusedResources]
    <color name="primary_800">#1e40af</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:27: Warning: The resource R.color.primary_900 appears to be unused [UnusedResources]
    <color name="primary_900">#1e3a8a</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:28: Warning: The resource R.color.primary_950 appears to be unused [UnusedResources]
    <color name="primary_950">#172554</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:32: Warning: The resource R.color.success_500 appears to be unused [UnusedResources]
    <color name="success_500">#10b981</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:34: Warning: The resource R.color.warning_50 appears to be unused [UnusedResources]
    <color name="warning_50">#fffbeb</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:35: Warning: The resource R.color.warning_500 appears to be unused [UnusedResources]
    <color name="warning_500">#f59e0b</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:37: Warning: The resource R.color.error_50 appears to be unused [UnusedResources]
    <color name="error_50">#fef2f2</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:38: Warning: The resource R.color.error_500 appears to be unused [UnusedResources]
    <color name="error_500">#ef4444</color>
           ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:42: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:43: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:51: Warning: The resource R.color.background_pending appears to be unused [UnusedResources]
    <color name="background_pending">#FFF8E1</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:52: Warning: The resource R.color.background_in_progress appears to be unused [UnusedResources]
    <color name="background_in_progress">#E3F2FD</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml:53: Warning: The resource R.color.background_completed appears to be unused [UnusedResources]
    <color name="background_completed">#E8F5E8</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\fab_background.xml:2: Warning: The resource R.drawable.fab_background appears to be unused [UnusedResources]
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\gradient_accent.xml:2: Warning: The resource R.drawable.gradient_accent appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_background.xml:2: Warning: The resource R.drawable.ic_launcher_background appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp: Warning: The resource R.mipmap.ic_launcher_round appears to be unused [UnusedResources]
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\modern_card_background.xml:2: Warning: The resource R.drawable.modern_card_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.task_hint appears to be unused [UnusedResources]
    <string name="task_hint">Yeni tapşırıq daxil edin</string>
            ~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.no_tasks appears to be unused [UnusedResources]
    <string name="no_tasks">Heç bir tapşırıq tapılmadı</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:99: Warning: The resource R.string.sync_now appears to be unused [UnusedResources]
    <string name="sync_now">İndi Sinxronizasiya Et</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:100: Warning: The resource R.string.sign_out appears to be unused [UnusedResources]
    <string name="sign_out">Çıxış</string>
            ~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:101: Warning: The resource R.string.signed_in_as appears to be unused [UnusedResources]
    <string name="signed_in_as">Daxil olmuş: %s</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:102: Warning: The resource R.string.sync_success appears to be unused [UnusedResources]
    <string name="sync_success">Sinxronizasiya tamamlandı</string>
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml:103: Warning: The resource R.string.sync_failed appears to be unused [UnusedResources]
    <string name="sync_failed">Sinxronizasiya xətası</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\task_item_background.xml:2: Warning: The resource R.drawable.task_item_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:190: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_filter.xml:67: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_sort.xml:89: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:61: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:18: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:30: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:103: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:147: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_filter.xml:41: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\Music\remote\todolist\app\build.gradle:38: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:39: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:40: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.cardview:cardview:1.0.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:43: Warning: Use version catalog instead [UseTomlInstead]
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:44: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.firebase:firebase-auth'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:45: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.firebase:firebase-firestore'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\build.gradle:46: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_login.xml:12: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\empty_state.xml:10: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:111: Warning: 'clickable' attribute found, please also add 'focusable' [KeyboardInaccessibleWidget]
            android:clickable="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml:155: Warning: 'clickable' attribute found, please also add 'focusable' [KeyboardInaccessibleWidget]
            android:clickable="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "KeyboardInaccessibleWidget":
   A widget that is declared to be clickable but not declared to be focusable
   is not accessible via the keyboard. Please add the focusable attribute as
   well.

C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\LoginActivity.java:46: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            btnGoogleSignIn.setText("Giriş edilir...");
                                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:94: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            totalTasksTextView.setText(getString(R.string.total_tasks_label) + ": 0");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:95: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            completedTasksTextView.setText(getString(R.string.status_completed) + ": 0");
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:96: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            pendingTasksTextView.setText(getString(R.string.status_pending) + ": 0");
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:97: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            inProgressTasksTextView.setText(getString(R.string.status_in_progress) + ": 0");
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:98: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            highPriorityTasksTextView.setText(getString(R.string.priority_high) + ": 0");
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:99: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + ": 0");
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:100: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            lowPriorityTasksTextView.setText(getString(R.string.priority_low) + ": 0");
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:101: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            completionRateTextView.setText(getString(R.string.completion_rate) + ": 0%");
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:141: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        totalTasksTextView.setText(getString(R.string.total_tasks_label) + ": " + totalTasks);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:142: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        completedTasksTextView.setText(getString(R.string.status_completed) + ": " + completedTasks);
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:143: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        pendingTasksTextView.setText(getString(R.string.status_pending) + ": " + pendingTasks);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:144: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        inProgressTasksTextView.setText(getString(R.string.status_in_progress) + ": " + inProgressTasks);
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:145: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        highPriorityTasksTextView.setText(getString(R.string.priority_high) + ": " + highPriorityTasks);
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:146: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + ": " + mediumPriorityTasks);
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:147: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        lowPriorityTasksTextView.setText(getString(R.string.priority_low) + ": " + lowPriorityTasks);
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\StatisticsActivity.java:148: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        completionRateTextView.setText(getString(R.string.completion_rate) + ": " + String.format(Locale.getDefault(), "%.2f", completionRate) + "%");
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\java\com\example\todolist\TaskAdapter.java:92: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            holder.taskReminderTime.setText(context.getString(R.string.task_reminder) + ": " + task.getFormattedReminderTime());
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:51: Warning: Hardcoded string "Task Title", should use @string resource [HardcodedText]
                android:text="Task Title"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:60: Warning: Hardcoded string "Orta", should use @string resource [HardcodedText]
                android:text="Orta"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:77: Warning: Hardcoded string "Pending", should use @string resource [HardcodedText]
                android:text="Pending"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:88: Warning: Hardcoded string "Task description goes here...", should use @string resource [HardcodedText]
            android:text="Task description goes here..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:113: Warning: Hardcoded string "01/01/2024 12:00", should use @string resource [HardcodedText]
                android:text="01/01/2024 12:00"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:123: Warning: Hardcoded string "Deadline: Yoxdur", should use @string resource [HardcodedText]
                android:text="Deadline: Yoxdur"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml:152: Warning: Hardcoded string "Reminder: 01/01/2024 11:00", should use @string resource [HardcodedText]
                android:text="Reminder: 01/01/2024 11:00"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

5 errors, 98 warnings
